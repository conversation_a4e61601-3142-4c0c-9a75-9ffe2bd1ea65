package cz.deuss.companyservice.blockchain.client

import io.ethers.core.types.Address
import java.math.BigInteger

/**
 * Client interface for bond registry blockchain operations. Handles role management
 * and other bond registry operations on the blockchain.
 */
interface BondRegistryClient {

    /**
     * Grants a role to a wallet address on the blockchain.
     * @param walletAddress The blockchain address of the wallet to grant the role to
     * @param role The role value to grant (as BigInteger)
     * @throws BondRegistryRoleException if role granting fails
     */
    fun grantRoleToWallet(walletAddress: Address, role: BigInteger)

}

/**
 * Exception thrown when bond registry role operations fail on the blockchain.
 */
class BondRegistryRoleException(message: String, cause: Throwable? = null) : Exception(message, cause)