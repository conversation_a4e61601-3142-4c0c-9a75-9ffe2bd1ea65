package cz.deuss.companyservice.blockchain.client

import cz.deuss.companyservice.blockchain.config.BlockchainConfiguration
import cz.deuss.platform.offchain.framework.logging.DeussLogger
import deuss.contract.company.BondRegistryV2
import io.ethers.core.types.Address
import io.ethers.providers.middleware.Middleware
import io.ethers.signers.PrivateKeySigner
import jakarta.inject.Singleton
import java.math.BigInteger

/**
 * Implementation of BondRegistryClient that interacts with the BondRegistryV2 smart contract
 * to manage roles and other bond registry operations on the blockchain.
 */
@Singleton
class BondRegistryClientImpl(
    private val middleware: Middleware,
    private val blockchainConfiguration: BlockchainConfiguration,
) : BondRegistryClient {

    private val logger = DeussLogger.semanticLogger(BondRegistryClientImpl::class)

    private val adminSigner: PrivateKeySigner by lazy {
        PrivateKeySigner(blockchainConfiguration.adminPrivateKey)
    }

    private val bondRegistryV2: BondRegistryV2 by lazy {
        BondRegistryV2(middleware, blockchainConfiguration.bondRegistryV2Address)
    }

    override fun grantRoleToWallet(walletAddress: Address, role: BigInteger) {
        logger.message("Granting role $role to wallet: $walletAddress").info()

        try {
            logger.message("Executing grantRoles transaction for wallet: $walletAddress with role: $role").debug()

            // Execute the blockchain transaction to grant the role
            val functionCall = bondRegistryV2.grantRoles(walletAddress, role)
            val pendingTransaction = functionCall.send(adminSigner).sendAwait().unwrap()

            // Wait for the transaction to be mined
            pendingTransaction.awaitInclusion().unwrap()

            logger.message("Successfully granted role $role to wallet: $walletAddress")
                .info()

        } catch (e: Exception) {
            logger.message("Failed to grant role $role to wallet: $walletAddress")
                .throwable(e)
                .error()
            throw BondRegistryRoleException("Failed to grant role $role to wallet: $walletAddress", e)
        }
    }
}
